"use client";

import {
    Page,
    Text,
    View,
    Document,
    StyleSheet,
    Image,
} from "@react-pdf/renderer";
import { getOrgDataField } from "../../../utils/cookies";

// Define invoice preferences interface
interface InvoicePrefs {
    logo?: string;
    sign?: string;
    autoinvno?: number;
    tandc?: string;
}

// Define orgDetails interfaces
interface orgFields {
    bankdetails: null | string;
    gstin: string;
    orgpan: string;
    orgaddr: string;
    orgpincode: string;
    invoice_preferences?: InvoicePrefs;
}

interface details {
    bisdetails: orgFields;
    olkstatus?: number;
}

// Cash Bill specific interfaces
interface CashBillContent {
    gsflag: number;
    gstflag: number;
    gstrate: number;
    quantity: number;
    gstamount: number;
    productcode: number;
    productname: string;
    freeQuantity: number;
    pricePerUnit: number;
    productAmount: number;
    taxableAmount: number;
    discountAmount: number;
}

interface CashBillRecord {
    invid: number;
    invoiceno: string;
    invoicedate: string;
    invnarration: string | null;
    taxflag: number;
    contents: CashBillContent[];
    amountpaid: string;
    invoicetotal: string;
    nettotal: string;
    icflag: number;
    roundoffflag: number;
    lockflag: number;
    discflag: number;
    taxstate: string;
    sourcestate: string;
    orgstategstin: string | null;
    attachment: string | null;
    attachmentcount: number;
    orgcode: number;
    paymentmode: number;
    inoutflag: number;
    invoicetotalword: string;
}

interface CashBillApiResponse {
    invrecord: CashBillRecord;
}

interface CashBillCombinedData extends CashBillApiResponse {
    orgDetails: details | null;
}

// Create styles - matching InvoicePdf.tsx structure and styling
const styles = StyleSheet.create({
    page: {
        fontSize: 10,
        padding: 30,
    },
    titleContainer: {
        textAlign: "center",
        marginBottom: 16,
    },
    title: {
        fontSize: 14,
        fontWeight: "bold",
        marginBottom: 8,
    },
    container: {
        marginVertical: 12,
        paddingVertical: 8,
    },
    header: {
        marginBottom: 5,
        borderBottomWidth: 1,
        borderBottomColor: "#000",
        paddingBottom: 5,
    },
    flexRow: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    flexCol: {
        flexDirection: "column",
    },
    billToContainer: {
        flexDirection: "row",
        gap: 24,
        justifyContent: "space-between",
        marginVertical: 12,
    },
    sectionContainer: {
        flex: 1,
        flexDirection: "column",
    },
    verticalDivider: {
        height: "auto",
        width: 1,
        backgroundColor: "#c2c2c2",
    },
    sectionTitle: {
        marginBottom: 4,
        fontWeight: "semibold",
        fontSize: 11,
    },
    boldText: {
        fontWeight: 500,
    },
    table: {
        width: "100%",
        marginVertical: 12,
        borderWidth: 1,
        borderColor: "#000",
    },
    tableHeader: {
        flexDirection: "row",
        borderBottomWidth: 1,
        borderBottomColor: "#a9b0ba",
        fontWeight: "semibold",
        backgroundColor: "#f5f5f5",
    },
    tableSubHeader: {
        flexDirection: "row",
        borderBottomWidth: 1,
        borderBottomColor: "black",
        fontWeight: "semibold",
        backgroundColor: "#f5f5f5",
    },
    tableRow: {
        flexDirection: "row",
        borderBottomWidth: 0.5,
        borderBottomColor: "#94979c",
    },
    tableTotalRow: {
        flexDirection: "row",
        borderTopWidth: 1,
        borderTopColor: "#000",
        fontWeight: "semibold",
    },
    cell: {
        padding: 3,
        textAlign: "center",
        borderRightWidth: 1,
        borderColor: "#000",
    },
    serialCell: {
        width: "5%",
    },
    descriptionCell: {
        width: "18%",
    },
    qtyCell: {
        width: "8%",
    },
    amountCell: {
        width: "12%",
    },
    gstCell: {
        width: "9.5%",
    },
    footer: {
        marginTop: 4,
        borderTopWidth: 1,
        borderTopColor: "#d3d3d3",
        paddingTop: 10,
        textAlign: "center",
    },
    footerText: {
        fontSize: 9,
        color: "#555555",
        marginBottom: 4,
    },
    paymentContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginVertical: 12,
        gap: 24,
        padding: 10,
        borderRadius: 4,
    },
    transportContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginVertical: 12,
        gap: 24,
    },
    logo: {
        width: 80,
        height: 60,
        objectFit: "contain",
        marginBottom: 8,
    },
    signature: {
        width: 100,
        height: 50,
        objectFit: "contain",
        marginTop: 8,
    },
});

// Utility function to format numbers in Indian style with commas
const formatIndianCurrency = (amount: number | string): string => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "0.00";

    // Convert to fixed 2 decimal places
    const fixedNum = num.toFixed(2);
    const [integerPart, decimalPart] = fixedNum.split(".");

    // Apply Indian number formatting (lakhs and crores)
    const lastThreeDigits = integerPart.slice(-3);
    const otherDigits = integerPart.slice(0, -3);

    if (otherDigits !== "") {
        const formattedOtherDigits = otherDigits.replace(
            /\B(?=(\d{2})+(?!\d))/g,
            ","
        );
        return `${formattedOtherDigits},${lastThreeDigits}.${decimalPart}`;
    } else {
        return `${lastThreeDigits}.${decimalPart}`;
    }
};

const CashBillPdf = ({ data }: { data: CashBillCombinedData }) => {
    console.log(data);

    // Safety check - ensure we have valid cash bill data
    if (!data || !data.invrecord) {
        return (
            <Document>
                <Page size="A4" style={styles.page}>
                    <View style={styles.titleContainer}>
                        <Text style={styles.title}>
                            Cash bill data not available
                        </Text>
                    </View>
                </Page>
            </Document>
        );
    }

    const { invrecord, orgDetails } = data;
    const orgname = getOrgDataField("orgname");
    const orgstate = getOrgDataField("orgstate");

    // Supported image formats for cash bill logos and signatures
    const SUPPORTED_IMAGE_FORMATS = ["png", "jpeg", "jpg", "svg"];
    const MAX_IMAGE_SIZE_KB = 1024; // 1MB max

    // Standard dimensions for different image types
    const STANDARD_DIMENSIONS = {
        logo: { width: 80, height: 60 },
        signature: { width: 100, height: 50 },
    };

    // Helper function to detect image format from base64 data
    const detectImageFormat = (base64String: string): string => {
        // Check the first few characters of base64 to detect format
        const header = base64String.substring(0, 20);

        // Common image format signatures in base64
        if (header.startsWith("/9j/") || header.startsWith("iVBORw0KGgo")) {
            return header.startsWith("/9j/") ? "jpeg" : "png";
        }

        // Try to decode and check magic bytes and content
        try {
            const binaryString = atob(base64String.substring(0, 100)); // Check more bytes for SVG
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // PNG signature: 89 50 4E 47
            if (
                bytes[0] === 0x89 &&
                bytes[1] === 0x50 &&
                bytes[2] === 0x4e &&
                bytes[3] === 0x47
            ) {
                return "png";
            }
            // JPEG signature: FF D8 FF
            if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
                return "jpeg";
            }

            // SVG detection - check for SVG content in decoded string
            const decodedContent = binaryString.toLowerCase();
            if (
                decodedContent.includes("<svg") ||
                decodedContent.includes("<?xml")
            ) {
                return "svg+xml";
            }
        } catch (e) {
            console.warn("Could not detect image format, defaulting to png");
        }

        return "png"; // Default fallback
    };

    // Helper function to validate image format
    const isValidImageFormat = (format: string): boolean => {
        const normalizedFormat = format.toLowerCase();
        // Handle svg+xml format
        if (normalizedFormat === "svg+xml") {
            return SUPPORTED_IMAGE_FORMATS.includes("svg");
        }
        return SUPPORTED_IMAGE_FORMATS.includes(normalizedFormat);
    };

    // Helper function to validate and render base64 image with proper format detection
    const renderBase64Image = (
        base64String: string | undefined,
        style: any,
        altText: string = "Image not available"
    ) => {
        if (!base64String || typeof base64String !== "string") {
            return altText ? <Text>{altText}</Text> : null;
        }

        try {
            // Basic validation - check if it's a reasonable length
            const cleanString = base64String.trim();
            if (cleanString.length === 0) {
                return altText ? <Text>{altText}</Text> : null;
            }

            // Check file size (base64 is ~33% larger than original)
            const estimatedSizeKB = (cleanString.length * 0.75) / 1024;
            if (estimatedSizeKB > MAX_IMAGE_SIZE_KB) {
                console.warn(
                    `Image too large: ${estimatedSizeKB.toFixed(
                        2
                    )}KB, max allowed: ${MAX_IMAGE_SIZE_KB}KB`
                );
                return <Text>Image too large (max {MAX_IMAGE_SIZE_KB}KB)</Text>;
            }

            // Detect image format
            const imageFormat = detectImageFormat(cleanString);
            console.log(
                `Detected image format: ${imageFormat}, size: ${estimatedSizeKB.toFixed(
                    2
                )}KB`
            );

            // Validate image format
            if (!isValidImageFormat(imageFormat)) {
                // console.warn(`Unsupported image format: ${imageFormat}`);
                return <Text>Unsupported image format: {imageFormat}</Text>;
            }

            // Create data URL with proper MIME type
            const mimeType =
                imageFormat === "svg+xml"
                    ? "image/svg+xml"
                    : `image/${imageFormat}`;
            const dataUrl = `data:${mimeType};base64,${cleanString}`;

            // Determine if this is a logo or signature based on style
            const isSignature =
                style?.width === STANDARD_DIMENSIONS.signature.width;
            const defaultDimensions = isSignature
                ? STANDARD_DIMENSIONS.signature
                : STANDARD_DIMENSIONS.logo;

            // Try to render the image with error handling and standard dimensions
            return (
                <Image
                    src={dataUrl}
                    style={{
                        // Use provided style dimensions, with fallback to standard dimensions
                        width: style?.width || defaultDimensions.width,
                        height: style?.height || defaultDimensions.height,
                        objectFit: "contain",
                        ...style, // Apply other style properties
                    }}
                />
            );
        } catch (error) {
            console.error("Error rendering image:", error);
            return altText ? <Text>{altText}</Text> : null;
        }
    };

    // Calculate totals
    const calculateTotals = () => {
        let totalTaxableAmount = 0;
        let totalGSTAmount = 0;
        let totalAmount = 0;

        invrecord.contents.forEach((item) => {
            totalTaxableAmount += item.taxableAmount;
            totalGSTAmount += item.gstamount;
            totalAmount += item.productAmount;
        });

        return {
            totalTaxableAmount,
            totalGSTAmount,
            totalAmount,
        };
    };

    const totals = calculateTotals();

    // Format date
    const formatDate = (dateString: string | null) => {
        if (!dateString) return "";
        const date = new Date(dateString);
        return date.toLocaleDateString("en-IN");
    };

    // Determine cash bill type
    const cashBillType = () => {
        switch (invrecord.inoutflag) {
            case 15:
                return "SALES CASH BILL";
            case 9:
                return "PURCHASE CASH BILL";
            default:
                return "CASH BILL";
        }
    };

    // Determine payment mode
    const paymentMode = () => {
        switch (invrecord.paymentmode) {
            case 3:
                return "Cash";
            case 2:
                return "Bank/Online";
            default:
                return "Unknown";
        }
    };

    return (
        <Document>
            <Page size="A4" style={styles.page}>
                {/* Title with proper spacing */}
                <View style={styles.titleContainer}>
                    <Text style={styles.title}>{cashBillType()}</Text>
                </View>

                {/* Header - matching InvoicePdf.tsx structure */}
                <View style={[styles.container, styles.header]}>
                    <View style={styles.flexRow}>
                        {/* Left side: Logo and Company details in horizontal layout */}
                        <View
                            style={{
                                flexDirection: "row",
                                alignItems: "flex-start",
                                flex: 1,
                            }}
                        >
                            {/* Logo on the left - only for sales cash bills */}
                            {invrecord.inoutflag === 15 &&
                                orgDetails?.bisdetails?.invoice_preferences
                                    ?.logo && (
                                    <View
                                        style={{ marginRight: 5, marginTop: 5 }}
                                    >
                                        {renderBase64Image(
                                            orgDetails.bisdetails
                                                .invoice_preferences.logo,
                                            styles.logo,
                                            "" // Empty string for no logo text
                                        )}
                                    </View>
                                )}

                            {/* Company details on the right of logo */}
                            <View style={{ flex: 1 }}>
                                <Text style={styles.sectionTitle}>
                                    {orgname || "N/A"}
                                </Text>
                                <Text>
                                    <Text style={styles.boldText}>
                                        Address:{" "}
                                    </Text>
                                    {orgDetails?.bisdetails?.orgaddr || "N/A"}
                                </Text>
                                <Text>
                                    {orgstate || "N/A"},{" "}
                                    {orgDetails?.bisdetails?.orgpincode ||
                                        "N/A"}
                                </Text>
                                {orgDetails?.bisdetails?.gstin && (
                                    <Text>
                                        <Text style={styles.boldText}>
                                            GSTIN:{" "}
                                        </Text>
                                        {orgDetails.bisdetails.gstin}
                                    </Text>
                                )}
                                {orgDetails?.bisdetails?.orgpan && (
                                    <Text>
                                        <Text style={styles.boldText}>
                                            PAN:{" "}
                                        </Text>
                                        {orgDetails.bisdetails.orgpan}
                                    </Text>
                                )}
                            </View>
                        </View>
                        <View
                            style={[
                                styles.flexCol,
                                { justifyContent: "flex-end" },
                            ]}
                        >
                            <Text>Cash Bill No: {invrecord.invoiceno}</Text>
                            <Text>
                                <Text style={styles.boldText}>
                                    Cash Bill Date:{" "}
                                </Text>
                                {formatDate(invrecord.invoicedate)}
                            </Text>
                            {/* <Text>
                                <Text style={styles.boldText}>
                                    Payment Mode:{" "}
                                </Text>
                                {paymentMode()}
                            </Text> */}
                        </View>
                    </View>
                </View>

                {/* Cash Bill Items Table - matching InvoicePdf.tsx structure */}
                <View style={styles.table}>
                    <View style={styles.tableHeader}>
                        <View style={[styles.cell, styles.serialCell]}>
                            <Text>S.no</Text>
                        </View>
                        <View style={[styles.cell, styles.descriptionCell]}>
                            <Text>DESCRIPTION</Text>
                        </View>
                        <View style={[styles.cell, styles.qtyCell]}>
                            <Text>QTY</Text>
                        </View>
                        <View style={[styles.cell, styles.qtyCell]}>
                            <Text>FREE QTY</Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>RATE</Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>AMOUNT</Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>TAXABLE AMOUNT</Text>
                        </View>
                        <View style={[styles.cell, { width: "19%" }]}>
                            <Text>GST</Text>
                        </View>
                        <View
                            style={[
                                styles.cell,
                                styles.amountCell,
                                { borderRight: 0 },
                            ]}
                        >
                            <Text>TOTAL</Text>
                        </View>
                    </View>

                    <View style={styles.tableSubHeader}>
                        <View style={[styles.cell, styles.serialCell]}></View>
                        <View style={[styles.cell, { width: "18%" }]}></View>
                        <View style={[styles.cell, { width: "8%" }]}></View>
                        <View style={[styles.cell, { width: "8%" }]}></View>
                        <View style={[styles.cell, { width: "12%" }]}></View>
                        <View style={[styles.cell, { width: "12%" }]}></View>
                        <View style={[styles.cell, { width: "12%" }]}></View>
                        <View style={[styles.cell, styles.gstCell]}>
                            <Text>Rate</Text>
                        </View>
                        <View style={[styles.cell, styles.gstCell]}>
                            <Text>Amt</Text>
                        </View>
                        <View
                            style={[
                                styles.cell,
                                { width: "12%", borderRight: 0 },
                            ]}
                        ></View>
                    </View>

                    {invrecord.contents.map((item, index) => (
                        <View key={index} style={styles.tableRow}>
                            <View style={[styles.cell, styles.serialCell]}>
                                <Text>{index + 1}</Text>
                            </View>
                            <View style={[styles.cell, styles.descriptionCell]}>
                                <Text>{item.productname}</Text>
                            </View>
                            <View style={[styles.cell, styles.qtyCell]}>
                                <Text>{item.quantity.toFixed(3)}</Text>
                            </View>
                            <View style={[styles.cell, styles.qtyCell]}>
                                <Text>{item.freeQuantity.toFixed(3)}</Text>
                            </View>
                            <View style={[styles.cell, styles.amountCell]}>
                                <Text>
                                    {formatIndianCurrency(item.pricePerUnit)}
                                </Text>
                            </View>
                            <View style={[styles.cell, styles.amountCell]}>
                                <Text>
                                    {formatIndianCurrency(item.productAmount)}
                                </Text>
                            </View>
                            <View style={[styles.cell, styles.amountCell]}>
                                <Text>
                                    {formatIndianCurrency(item.taxableAmount)}
                                </Text>
                            </View>
                            <View style={[styles.cell, styles.gstCell]}>
                                <Text>{item.gstrate.toFixed(2)}%</Text>
                            </View>
                            <View style={[styles.cell, styles.gstCell]}>
                                <Text>
                                    {formatIndianCurrency(item.gstamount)}
                                </Text>
                            </View>
                            <View
                                style={[
                                    styles.cell,
                                    styles.amountCell,
                                    { borderRight: 0 },
                                ]}
                            >
                                <Text>
                                    {formatIndianCurrency(
                                        item.taxableAmount + item.gstamount
                                    )}
                                </Text>
                            </View>
                        </View>
                    ))}

                    <View style={styles.tableTotalRow}>
                        <View
                            style={[
                                styles.cell,
                                styles.serialCell,
                                { borderRight: 0 },
                            ]}
                        ></View>
                        <View style={[styles.cell, { width: "34%" }]}>
                            <Text>TOTAL</Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>
                                {formatIndianCurrency(totals.totalAmount)}
                            </Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>
                                {formatIndianCurrency(0)}{" "}
                                {/* No discount in cash bills */}
                            </Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>
                                {formatIndianCurrency(
                                    totals.totalTaxableAmount
                                )}
                            </Text>
                        </View>
                        <View style={[styles.cell, styles.gstCell]}></View>
                        <View style={[styles.cell, styles.gstCell]}>
                            <Text>
                                {formatIndianCurrency(totals.totalGSTAmount)}
                            </Text>
                        </View>
                        <View
                            style={[
                                styles.cell,
                                styles.amountCell,
                                { borderRight: 0 },
                            ]}
                        >
                            <Text>
                                {formatIndianCurrency(
                                    totals.totalTaxableAmount +
                                        totals.totalGSTAmount
                                )}
                            </Text>
                        </View>
                    </View>
                </View>

                {/* Payment Details and Totals - matching InvoicePdf.tsx structure */}
                <View style={[styles.container, styles.paymentContainer]}>
                    <View style={styles.sectionContainer}>
                        <Text style={styles.sectionTitle}>Payment Details</Text>
                        <Text>
                            <Text style={styles.boldText}>Payment Mode: </Text>
                            {paymentMode()}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>Bank Details: </Text>
                            {orgDetails?.bisdetails?.bankdetails || "N/A"}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>Amount Paid: </Text>
                            {formatIndianCurrency(invrecord.amountpaid)}
                        </Text>
                    </View>

                    <View style={styles.verticalDivider} />

                    <View style={styles.sectionContainer}>
                        <Text>
                            <Text style={styles.boldText}>
                                Total Taxable Amount:{" "}
                            </Text>
                            {formatIndianCurrency(totals.totalTaxableAmount)}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>Total GST: </Text>
                            {formatIndianCurrency(totals.totalGSTAmount)}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>
                                Total Cash Bill Amount:{" "}
                            </Text>
                            {formatIndianCurrency(invrecord.invoicetotal)}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>Net Total: </Text>
                            {formatIndianCurrency(invrecord.nettotal)}
                        </Text>
                    </View>
                </View>

                {/* Amount in words - matching InvoicePdf.tsx structure */}
                <View style={styles.container}>
                    <Text style={{ textAlign: "center" }}>
                        <Text style={styles.boldText}>Amount in words: </Text>
                        {(invrecord.invoicetotalword || "N/A") + " Only"}
                    </Text>
                </View>

                {/* Narration */}
                {invrecord.invnarration && (
                    <View style={styles.container}>
                        <Text style={styles.sectionTitle}>Narration:</Text>
                        <Text>{invrecord.invnarration}</Text>
                    </View>
                )}

                {/* Terms and Conditions and Signature in one row - only for sales cash bills */}
                {(() => {
                    // Only show invoice preferences for sales cash bills (inoutflag === 15)
                    if (invrecord.inoutflag !== 15) {
                        return null;
                    }

                    // Check if terms and conditions exist
                    const hasTerms =
                        orgDetails?.bisdetails?.invoice_preferences?.tandc &&
                        orgDetails.bisdetails.invoice_preferences.tandc.trim() !==
                            "";

                    // Check if signature exists
                    const hasSignature =
                        orgDetails?.bisdetails?.invoice_preferences?.sign &&
                        orgDetails.bisdetails.invoice_preferences.sign.trim() !==
                            "";

                    // Check if signature is the only section (for right alignment)
                    const isSignatureOnly = hasSignature && !hasTerms;

                    // If we have terms or signature, render the appropriate sections
                    if (hasTerms || hasSignature) {
                        return (
                            <View
                                style={[
                                    styles.container,
                                    isSignatureOnly
                                        ? {
                                              flexDirection: "row",
                                              justifyContent: "flex-end",
                                              marginVertical: 12,
                                              gap: 24,
                                          }
                                        : styles.transportContainer,
                                ]}
                            >
                                {/* Terms and Conditions - only if they exist */}
                                {hasTerms && (
                                    <View style={styles.sectionContainer}>
                                        <Text style={styles.sectionTitle}>
                                            Terms and Conditions
                                        </Text>
                                        <Text>
                                            {
                                                orgDetails?.bisdetails
                                                    ?.invoice_preferences?.tandc
                                            }
                                        </Text>
                                    </View>
                                )}

                                {/* Add divider between terms and signature if both exist */}
                                {hasTerms && hasSignature && (
                                    <View style={styles.verticalDivider} />
                                )}

                                {/* Signature - only if it exists */}
                                {hasSignature && (
                                    <View style={styles.sectionContainer}>
                                        <Text
                                            style={
                                                isSignatureOnly
                                                    ? {
                                                          ...styles.sectionTitle,
                                                          textAlign: "right",
                                                      }
                                                    : styles.sectionTitle
                                            }
                                        >
                                            Signature
                                        </Text>
                                        <View
                                            style={{
                                                alignItems: isSignatureOnly
                                                    ? "flex-end"
                                                    : "center",
                                            }}
                                        >
                                            {renderBase64Image(
                                                orgDetails?.bisdetails
                                                    ?.invoice_preferences?.sign,
                                                styles.signature,
                                                "" // Empty string for no fallback text
                                            )}
                                            <Text style={styles.footerText}>
                                                Authorized Signature
                                            </Text>
                                        </View>
                                    </View>
                                )}
                            </View>
                        );
                    }

                    // If none of the sections have data, don't render anything
                    return null;
                })()}

                {/* Footer - matching InvoicePdf.tsx structure */}
                <View style={styles.footer}>
                    <Text style={styles.footerText}>
                        Thank you for your business!
                    </Text>
                    {/* <Text style={styles.footerText}>
                        Generated on: {new Date().toLocaleDateString("en-IN")}{" "}
                        at {new Date().toLocaleTimeString("en-IN")}
                    </Text> */}
                </View>
            </Page>
        </Document>
    );
};

export default CashBillPdf;

// Export interfaces for use in other components
export type {
    CashBillCombinedData,
    CashBillApiResponse,
    CashBillRecord,
    CashBillContent,
};
